package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"iaa-gamelog/internal/models"
)

// DecodeData DECODE_DATA - 解密微信数据
func DecodeData(c *gin.Context) {
	var req struct {
		AppName       string `json:"app_name" form:"app_name"`
		EncryptedData string `json:"encrypted_data" form:"encrypted_data"`
		IV            string `json:"iv" form:"iv"`
		SessionKey    string `json:"session_key" form:"session_key"`
	}

	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "请求参数错误",
		})
		return
	}

	if req.AppName == "" || req.EncryptedData == "" || req.IV == "" || req.SessionKey == "" {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 模拟解密微信数据
	// 实际应用中需要使用微信提供的解密算法
	decryptedData := map[string]interface{}{
		"openId":    "decrypted_openid_" + req.AppName,
		"nickName":  "微信用户",
		"gender":    1,
		"city":      "深圳",
		"province":  "广东",
		"country":   "中国",
		"avatarUrl": "https://example.com/avatar.jpg",
		"unionId":   "decrypted_unionid_" + req.AppName,
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: decryptedData,
	})
}

// ShareHit BMS_CARD_SHARE - 分享卡片
func ShareHit(c *gin.Context) {
	var req struct {
		AppName   string `json:"app_name" form:"app_name"`
		Version   string `json:"version" form:"version"`
		UUID      string `json:"uuid" form:"uuid"`
		ShareType string `json:"share_type" form:"share_type"`
		ShareID   string `json:"share_id" form:"share_id"`
	}

	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "请求参数错误",
		})
		return
	}

	if req.AppName == "" || req.UUID == "" {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 模拟分享卡片处理
	shareData := map[string]interface{}{
		"share_id":     req.ShareID,
		"share_type":   req.ShareType,
		"share_result": "success",
		"reward":       100,
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: shareData,
	})
}

// ShareInfo BMS_CARD_SHARE_INFO - 分享信息
func ShareInfo(c *gin.Context) {
	appName := c.Query("app_name")
	version := c.Query("version")
	uuid := c.Query("uuid")

	if appName == "" || uuid == "" {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 模拟分享信息
	shareInfo := map[string]interface{}{
		"app_name": appName,
		"version":  version,
		"uuid":     uuid,
		"shares": []map[string]interface{}{
			{
				"id":          "share_001",
				"title":       "邀请好友",
				"description": "邀请好友一起玩游戏",
				"image_url":   "https://example.com/share1.jpg",
				"reward":      50,
				"limit":       5,
				"used":        2,
			},
			{
				"id":          "share_002",
				"title":       "朋友圈分享",
				"description": "分享游戏到朋友圈",
				"image_url":   "https://example.com/share2.jpg",
				"reward":      100,
				"limit":       3,
				"used":        1,
			},
		},
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: shareInfo,
	})
}
