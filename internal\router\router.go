package router

import (
	"log"
	"net/http"

	"iaa-gamelog/internal/config"
	"iaa-gamelog/internal/database"
	"iaa-gamelog/internal/handlers"
	"iaa-gamelog/internal/middleware"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// SetupRouter 设置路由
func SetupRouter(appConfig *config.Config, db database.DatabaseInterface) *gin.Engine {
	// 设置Gin为发布模式
	gin.SetMode(gin.ReleaseMode)

	// 创建路由
	r := gin.Default()

	// 配置CORS中间件
	if appConfig.Server.CORS.Enabled {
		corsConfig := cors.DefaultConfig()
		corsConfig.AllowOrigins = appConfig.Server.CORS.AllowedOrigins
		corsConfig.AllowMethods = appConfig.Server.CORS.AllowedMethods
		corsConfig.AllowHeaders = appConfig.Server.CORS.AllowedHeaders
		r.Use(cors.New(corsConfig))
		log.Println("CORS中间件已启用")
	}

	// BaseNet API路由 - 游戏配置相关
	common := r.Group("/common")
	{
		// 游戏配置
		common.GET("/config/info", handlers.GetLaunchConfig)    // BMS_LAUNCH_CONFIG
		common.GET("/game/share_list", handlers.GetShareConfig) // BMS_SHARE_CONFIG
		common.GET("/game/ads", handlers.GetAdsConfig)          // BMS_TOFU_CONFIG

		// 用户登录
		common.POST("/session/sign_in", handlers.SignInWechat) // BMS_SIGN_IN_WX
		common.POST("/baidu/sign_in", handlers.SignInBaidu)    // BMS_SIGN_IN_BD
		common.POST("/qqminiapp/sign_in", handlers.SignInQQ)   // BMS_SIGN_IN_QQ

		// 数据存储
		common.POST("/game-data/s-save", middleware.VerifySignature(appConfig), handlers.DataSave(db)) // DATA_SAVE (需要签名验证)
		common.GET("/game-data/get", handlers.DataGet(db))                                             // DATA_GET
		common.GET("/game-data/multi-get", handlers.DataMultiGet(db))                                  // DATA_MULTIGET

		// 工具类接口
		common.GET("/common/time", handlers.GetServerTime)       // BMS_SERVER_TIME
		common.GET("/is/is", handlers.CheckIP)                   // BMS_IP_IS_ENABLE
		common.GET("/login-code/check", handlers.CheckLoginCode) // LOGINCODE

		// 微信相关接口
		common.POST("/wechat/decode_data", handlers.DecodeData) // DECODE_DATA
		common.POST("/share/hit", handlers.ShareHit)            // BMS_CARD_SHARE
		common.GET("/share/info", handlers.ShareInfo)           // BMS_CARD_SHARE_INFO

		// 头条相关接口
		common.POST("/toutiao/antidirt", handlers.AntiDirt)                                   // ANTIDIRT
		common.POST("/toutiao/gift-receive-reward", handlers.GiftReceiveReward)               // GIFT_RECEIVE_REWARD
		common.GET("/toutiao/get-unconsumed-circle-gifts", handlers.GetUnconsumedCircleGifts) // GET_UNCONSUMED_CIRCLE_GIFTS
		common.POST("/toutiao/consume-circle-gift", handlers.ConsumeCircleGift)               // CONSUME_CIRCLE_GIFT

		// 其他接口
		common.GET("/ads/material-ss", handlers.MaterialSS) // MATERIALSS
	}

	// 统计上报API路由
	statistics := r.Group("/statistics")
	{
		statistics.POST("/login_log", handlers.LoginLog(db))   // BMS_LOGIN_LOG
		statistics.POST("/game", handlers.GameStats(db))       // BMS_GAME
		statistics.POST("/ad/show", handlers.AdShow(db))       // BMS_AD_SHOW
		statistics.POST("/ad/hit", handlers.AdHit(db))         // BMS_AD_HIT
		statistics.POST("/share/show", handlers.ShareShow(db)) // BMS_SHARE_SHOW
		statistics.POST("/hint", handlers.Hint(db))            // BMS_HINT
	}

	// 其他特殊路由
	api := r.Group("/api")
	{
		api.GET("/sdk/yw/adv/getAdvInfo", handlers.GetAdvInfo) // GETADVINFO
	}

	check := r.Group("/check")
	{
		check.GET("/Uuidwhitelist/uuidInWhitelist", handlers.UuidInWhitelist) // UUID_IN_WHITE_LIST
	}

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	return r
}

// LogRoutes 打印路由信息
func LogRoutes() {
	log.Println("BaseNet API接口:")
	log.Println("游戏配置:")
	log.Println("  GET  /common/config/info - 获取游戏启动配置")
	log.Println("  GET  /common/game/share_list - 获取分享配置")
	log.Println("  GET  /common/game/ads - 获取广告配置")
	log.Println("用户登录:")
	log.Println("  POST /common/session/sign_in - 微信登录")
	log.Println("  POST /common/baidu/sign_in - 百度登录")
	log.Println("  POST /common/qqminiapp/sign_in - QQ登录")
	log.Println("数据存储:")
	log.Println("  POST /common/game-data/s-save - 保存游戏数据 (需要签名)")
	log.Println("  GET  /common/game-data/get - 获取游戏数据")
	log.Println("  GET  /common/game-data/multi-get - 批量获取游戏数据")
	log.Println("工具类:")
	log.Println("  GET  /common/common/time - 获取服务器时间")
	log.Println("  GET  /common/is/is - IP检查")
	log.Println("  GET  /common/login-code/check - 登录码验证")
	log.Println("微信相关:")
	log.Println("  POST /common/wechat/decode_data - 解密微信数据")
	log.Println("  POST /common/share/hit - 分享卡片")
	log.Println("  GET  /common/share/info - 分享信息")
	log.Println("头条相关:")
	log.Println("  POST /common/toutiao/antidirt - 内容审核")
	log.Println("  POST /common/toutiao/gift-receive-reward - 礼品奖励")
	log.Println("  GET  /common/toutiao/get-unconsumed-circle-gifts - 获取未消费圈子礼品")
	log.Println("  POST /common/toutiao/consume-circle-gift - 消费圈子礼品")
	log.Println("其他接口:")
	log.Println("  GET  /common/ads/material-ss - 素材接口")
	log.Println("  GET  /api/sdk/yw/adv/getAdvInfo - 获取广告信息")
	log.Println("  GET  /check/Uuidwhitelist/uuidInWhitelist - UUID白名单检查")
	log.Println("统计上报:")
	log.Println("  POST /statistics/login_log - 登录日志统计")
	log.Println("  POST /statistics/game - 游戏行为统计")
	log.Println("  POST /statistics/ad/show - 广告展示统计")
	log.Println("  POST /statistics/ad/hit - 广告点击统计")
	log.Println("  POST /statistics/share/show - 分享展示统计")
	log.Println("  POST /statistics/hint - 提示统计")
	log.Println("系统:")
	log.Println("  GET  /health - 健康检查")
}
