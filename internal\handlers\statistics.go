package handlers

import (
	"log"
	"net/http"
	"time"

	"iaa-gamelog/internal/database"
	"iaa-gamelog/internal/models"

	"github.com/gin-gonic/gin"
)

// LoginLog BMS_LOGIN_LOG - 登录日志统计
func LoginLog(db database.DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req models.LoginLogRequest
		if err := c.ShouldBind(&req); err != nil {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "请求参数错误",
			})
			return
		}

		if req.AppName == "" || req.UUID == "" {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "缺少必要参数",
			})
			return
		}

		// 记录登录日志到数据库
		logData := map[string]interface{}{
			"type":        "login_log",
			"app_name":    req.AppName,
			"channel":     req.Channel,
			"version":     req.Version,
			"uuid":        req.UUID,
			"platform":    req.Platform,
			"device_info": req.DeviceInfo,
			"timestamp":   time.Now().Unix(),
		}

		_, _, err := db.SaveRecord(req.UUID, "stat_login_log", logData)
		if err != nil {
			log.Printf("保存登录日志失败: %v", err)
		}

		c.JSON(http.StatusOK, models.StandardResponse{
			Code: 0,
			Msg:  "success",
		})
	}
}

// GameStats BMS_GAME - 游戏行为统计
func GameStats(db database.DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req models.GameStatRequest
		if err := c.ShouldBind(&req); err != nil {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "请求参数错误",
			})
			return
		}

		if req.AppName == "" || req.UUID == "" {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "缺少必要参数",
			})
			return
		}

		// 记录游戏行为统计
		logData := map[string]interface{}{
			"type":      "game_stats",
			"app_name":  req.AppName,
			"channel":   req.Channel,
			"version":   req.Version,
			"uuid":      req.UUID,
			"m_data":    req.MData,
			"b_t":       req.BT,
			"timestamp": time.Now().Unix(),
		}

		_, _, err := db.SaveRecord(req.UUID, "stat_game_behavior", logData)
		if err != nil {
			log.Printf("保存游戏行为统计失败: %v", err)
		}

		c.JSON(http.StatusOK, models.StandardResponse{
			Code: 0,
			Msg:  "success",
		})
	}
}

// AdShow BMS_AD_SHOW - 广告展示统计
func AdShow(db database.DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req models.AdStatRequest
		if err := c.ShouldBind(&req); err != nil {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "请求参数错误",
			})
			return
		}

		if req.AppName == "" || req.UUID == "" || req.AdType == "" {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "缺少必要参数",
			})
			return
		}

		// 记录广告展示统计
		logData := map[string]interface{}{
			"type":      "ad_show",
			"app_name":  req.AppName,
			"version":   req.Version,
			"uuid":      req.UUID,
			"ad_type":   req.AdType,
			"ad_id":     req.AdID,
			"platform":  req.Platform,
			"timestamp": req.Timestamp,
		}

		_, _, err := db.SaveRecord(req.UUID, "stat_ad_show", logData)
		if err != nil {
			log.Printf("保存广告展示统计失败: %v", err)
		}

		c.JSON(http.StatusOK, models.StandardResponse{
			Code: 0,
			Msg:  "success",
		})
	}
}

// AdHit BMS_AD_HIT - 广告点击统计
func AdHit(db database.DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req models.AdStatRequest
		if err := c.ShouldBind(&req); err != nil {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "请求参数错误",
			})
			return
		}

		if req.AppName == "" || req.UUID == "" || req.AdType == "" {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "缺少必要参数",
			})
			return
		}

		// 记录广告点击统计
		logData := map[string]interface{}{
			"type":      "ad_hit",
			"app_name":  req.AppName,
			"version":   req.Version,
			"uuid":      req.UUID,
			"ad_type":   req.AdType,
			"ad_id":     req.AdID,
			"platform":  req.Platform,
			"timestamp": req.Timestamp,
		}

		_, _, err := db.SaveRecord(req.UUID, "stat_ad_hit", logData)
		if err != nil {
			log.Printf("保存广告点击统计失败: %v", err)
		}

		c.JSON(http.StatusOK, models.StandardResponse{
			Code: 0,
			Msg:  "success",
		})
	}
}

// ShareShow BMS_SHARE_SHOW - 分享展示统计
func ShareShow(db database.DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			AppName   string `json:"app_name" form:"app_name"`
			Version   string `json:"version" form:"version"`
			UUID      string `json:"uuid" form:"uuid"`
			ShareType string `json:"share_type" form:"share_type"`
			ShareID   string `json:"share_id" form:"share_id"`
			Platform  string `json:"platform" form:"platform"`
		}

		if err := c.ShouldBind(&req); err != nil {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "请求参数错误",
			})
			return
		}

		if req.AppName == "" || req.UUID == "" {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "缺少必要参数",
			})
			return
		}

		// 记录分享展示统计
		logData := map[string]interface{}{
			"type":       "share_show",
			"app_name":   req.AppName,
			"version":    req.Version,
			"uuid":       req.UUID,
			"share_type": req.ShareType,
			"share_id":   req.ShareID,
			"platform":   req.Platform,
			"timestamp":  time.Now().Unix(),
		}

		_, _, err := db.SaveRecord(req.UUID, "stat_share_show", logData)
		if err != nil {
			log.Printf("保存分享展示统计失败: %v", err)
		}

		c.JSON(http.StatusOK, models.StandardResponse{
			Code: 0,
			Msg:  "success",
		})
	}
}

// Hint BMS_HINT - 提示统计
func Hint(db database.DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			AppName  string `json:"app_name" form:"app_name"`
			Version  string `json:"version" form:"version"`
			UUID     string `json:"uuid" form:"uuid"`
			HintType string `json:"hint_type" form:"hint_type"`
			HintID   string `json:"hint_id" form:"hint_id"`
			Action   string `json:"action" form:"action"` // show, click, close
			Content  string `json:"content" form:"content"`
		}

		if err := c.ShouldBind(&req); err != nil {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "请求参数错误",
			})
			return
		}

		if req.AppName == "" || req.UUID == "" || req.HintType == "" {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "缺少必要参数",
			})
			return
		}

		// 记录提示统计
		logData := map[string]interface{}{
			"type":      "hint",
			"app_name":  req.AppName,
			"version":   req.Version,
			"uuid":      req.UUID,
			"hint_type": req.HintType,
			"hint_id":   req.HintID,
			"action":    req.Action,
			"content":   req.Content,
			"timestamp": time.Now().Unix(),
		}

		_, _, err := db.SaveRecord(req.UUID, "stat_hint", logData)
		if err != nil {
			log.Printf("保存提示统计失败: %v", err)
		}

		c.JSON(http.StatusOK, models.StandardResponse{
			Code: 0,
			Msg:  "success",
		})
	}
}
