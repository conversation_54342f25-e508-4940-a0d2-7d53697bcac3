package main

import (
	"fmt"
	"os"
	"strings"
	"time"

	"iaa-gamelog/test/common"
)

// testServerTime 测试获取服务器时间
func testServerTime(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试服务器时间获取\n")

	url := fmt.Sprintf("%s/common/common/time", config.BaseURL)
	resp, err := common.HTTPGet(config, url)
	if err != nil {
		common.PrintTestResult("获取服务器时间", false, err)
		return err
	}

	if resp.Code == 0 {
		common.PrintTestResult("获取服务器时间", true, nil)
		if data, ok := resp.Data.(map[string]interface{}); ok {
			if serverTime, exists := data["time"]; exists {
				if timeFloat, ok := serverTime.(float64); ok {
					serverTimestamp := int64(timeFloat)
					currentTime := time.Now().Unix()
					timeDiff := currentTime - serverTimestamp

					fmt.Printf("     ✓ 服务器时间戳: %d\n", serverTimestamp)
					fmt.Printf("     ✓ 本地时间戳: %d\n", currentTime)
					fmt.Printf("     ✓ 时间差: %d秒\n", timeDiff)

					// 检查时间差是否合理（应该在几秒内）
					if timeDiff < -10 || timeDiff > 10 {
						fmt.Printf("     ⚠️  时间差较大，可能存在时钟同步问题\n")
					}
				}
			}
		}
	} else {
		common.PrintTestResult("获取服务器时间", false, fmt.Errorf("响应码: %d, 消息: %s", resp.Code, resp.Msg))
	}

	return nil
}

// testIPCheck 测试IP检查
func testIPCheck(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试IP检查功能\n")

	url := fmt.Sprintf("%s/common/is/is", config.BaseURL)
	resp, err := common.HTTPGet(config, url)
	if err != nil {
		common.PrintTestResult("IP检查", false, err)
		return err
	}

	if resp.Code == 0 {
		common.PrintTestResult("IP检查", true, nil)
		if data, ok := resp.Data.(map[string]interface{}); ok {
			if ip, exists := data["ip"]; exists {
				fmt.Printf("     ✓ 客户端IP: %v\n", ip)
			}
			if enabled, exists := data["enabled"]; exists {
				fmt.Printf("     ✓ IP状态: %v\n", enabled)
				if enabledBool, ok := enabled.(bool); ok {
					if enabledBool {
						fmt.Printf("     ✓ IP访问已启用\n")
					} else {
						fmt.Printf("     ⚠️  IP访问被禁用\n")
					}
				}
			}
		}
	} else {
		common.PrintTestResult("IP检查", false, fmt.Errorf("响应码: %d, 消息: %s", resp.Code, resp.Msg))
	}

	return nil
}

// testLoginCode 测试登录码验证
func testLoginCode(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试登录码验证\n")

	testCases := []struct {
		name          string
		appName       string
		version       string
		code          string
		expectSuccess bool
	}{
		{"有效登录码", "test_game", "1.0.0", "valid123456", true},
		{"长登录码", "test_game", "1.0.0", "very_long_login_code_789", true},
		{"短登录码", "test_game", "1.0.0", "short", true},
		{"空登录码", "test_game", "1.0.0", "", false},
		{"缺少app_name", "", "1.0.0", "test123456", false},
	}

	for _, tc := range testCases {
		fmt.Printf("  🔑 %s\n", tc.name)

		url := fmt.Sprintf("%s/common/login-code/check?app_name=%s&version=%s&code=%s",
			config.BaseURL, tc.appName, tc.version, tc.code)

		resp, err := common.HTTPGet(config, url)
		if err != nil {
			if tc.expectSuccess {
				common.PrintTestResult(tc.name, false, err)
			} else {
				common.PrintTestResult(tc.name, true, nil)
			}
			continue
		}

		if tc.expectSuccess {
			if resp.Code == 0 {
				// 检查响应数据
				if data, ok := resp.Data.(map[string]interface{}); ok {
					if caCode, exists := data["ca_code"]; exists {
						if caCodeStr, ok := caCode.(string); ok {
							// 验证ca_code是32位MD5字符串
							if len(caCodeStr) == 32 {
								common.PrintTestResult(tc.name, true, nil)
								fmt.Printf("     ✓ 登录码: %s\n", tc.code)
								fmt.Printf("     ✓ CA码: %s\n", caCodeStr)
							} else {
								common.PrintTestResult(tc.name, false,
									fmt.Errorf("CA码长度不正确，期望32位，实际: %d位", len(caCodeStr)))
							}
						} else {
							common.PrintTestResult(tc.name, false, fmt.Errorf("ca_code字段类型错误"))
						}
					} else {
						common.PrintTestResult(tc.name, false, fmt.Errorf("响应中缺少ca_code字段"))
					}
				} else {
					common.PrintTestResult(tc.name, false, fmt.Errorf("响应数据格式错误"))
				}
			} else {
				common.PrintTestResult(tc.name, false, fmt.Errorf("响应码: %d, 消息: %s", resp.Code, resp.Msg))
			}
		} else {
			if resp.Code != 0 {
				common.PrintTestResult(tc.name, true, nil)
			} else {
				common.PrintTestResult(tc.name, false, fmt.Errorf("应该返回错误但成功了"))
			}
		}
	}

	return nil
}

// testHealthCheck 测试健康检查
func testHealthCheck(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试健康检查\n")

	url := fmt.Sprintf("%s/health", config.BaseURL)
	resp, err := common.HTTPGet(config, url)
	if err != nil {
		common.PrintTestResult("健康检查", false, err)
		return err
	}

	// 健康检查可能不是标准响应格式
	if resp.Data != nil {
		if data, ok := resp.Data.(map[string]interface{}); ok {
			if status, exists := data["status"]; exists && status == "ok" {
				common.PrintTestResult("健康检查", true, nil)
				fmt.Printf("     ✓ 服务状态: %v\n", status)
				return nil
			}
		}
	}

	common.PrintTestResult("健康检查", false, fmt.Errorf("健康检查响应格式异常"))
	return fmt.Errorf("健康检查失败")
}

func main() {
	config := common.DefaultTestConfig()

	// 检查命令行参数
	if len(os.Args) > 1 {
		if os.Args[1] == "--quiet" {
			config.Verbose = false
		}
	}

	common.PrintTestHeader("BaseNet API - 工具类模块测试")
	fmt.Printf("服务器地址: %s\n", config.BaseURL)

	// 检查服务器状态
	if err := common.WaitForServer(config, 3); err != nil {
		fmt.Printf("❌ 服务器连接失败: %v\n", err)
		fmt.Println("请确保服务器已启动: ./gamelog.exe")
		os.Exit(1)
	}

	// 执行测试
	var hasError bool

	if err := testServerTime(config); err != nil {
		hasError = true
	}

	if err := testIPCheck(config); err != nil {
		hasError = true
	}

	if err := testLoginCode(config); err != nil {
		hasError = true
	}

	if err := testHealthCheck(config); err != nil {
		hasError = true
	}

	// 输出测试总结
	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")
	if hasError {
		fmt.Println("❌ 工具类模块测试完成，存在失败项")
		os.Exit(1)
	} else {
		fmt.Println("✅ 工具类模块测试全部通过")
	}
}
