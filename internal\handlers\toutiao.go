package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"iaa-gamelog/internal/models"
)

// AntiDirt ANTIDIRT - 内容审核
func AntiDirt(c *gin.Context) {
	var req struct {
		AppName string `json:"app_name" form:"app_name"`
		Version string `json:"version" form:"version"`
		UUID    string `json:"uuid" form:"uuid"`
		Content string `json:"content" form:"content"`
		Type    string `json:"type" form:"type"` // text, image, etc.
	}

	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "请求参数错误",
		})
		return
	}

	if req.AppName == "" || req.Content == "" {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 模拟内容审核
	auditResult := map[string]interface{}{
		"audit_id":     "audit_" + req.UUID + "_001",
		"status":       "pass", // pass, reject, review
		"reason":       "",
		"suggestions":  []string{},
		"risk_level":   "low", // low, medium, high
		"audit_time":   1234567890,
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: auditResult,
	})
}

// GiftReceiveReward GIFT_RECEIVE_REWARD - 礼品奖励
func GiftReceiveReward(c *gin.Context) {
	var req struct {
		AppName string `json:"app_name" form:"app_name"`
		Version string `json:"version" form:"version"`
		UUID    string `json:"uuid" form:"uuid"`
		GiftID  string `json:"gift_id" form:"gift_id"`
	}

	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "请求参数错误",
		})
		return
	}

	if req.AppName == "" || req.UUID == "" || req.GiftID == "" {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 模拟礼品奖励处理
	rewardData := map[string]interface{}{
		"gift_id":     req.GiftID,
		"reward_type": "coins",
		"amount":      500,
		"status":      "received",
		"expire_time": 1234567890,
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: rewardData,
	})
}

// GetUnconsumedCircleGifts GET_UNCONSUMED_CIRCLE_GIFTS - 获取未消费圈子礼品
func GetUnconsumedCircleGifts(c *gin.Context) {
	appName := c.Query("app_name")
	version := c.Query("version")
	uuid := c.Query("uuid")

	if appName == "" || uuid == "" {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 模拟未消费圈子礼品
	gifts := []map[string]interface{}{
		{
			"gift_id":     "gift_001",
			"gift_name":   "金币礼包",
			"gift_type":   "coins",
			"amount":      1000,
			"from_user":   "friend_001",
			"message":     "恭喜通关！",
			"expire_time": 1234567890,
		},
		{
			"gift_id":     "gift_002",
			"gift_name":   "道具礼包",
			"gift_type":   "items",
			"items": []map[string]interface{}{
				{"item_id": "item_001", "count": 5},
				{"item_id": "item_002", "count": 3},
			},
			"from_user":   "friend_002",
			"message":     "加油！",
			"expire_time": 1234567890,
		},
	}

	giftData := map[string]interface{}{
		"app_name": appName,
		"version":  version,
		"uuid":     uuid,
		"gifts":    gifts,
		"total":    len(gifts),
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: giftData,
	})
}

// ConsumeCircleGift CONSUME_CIRCLE_GIFT - 消费圈子礼品
func ConsumeCircleGift(c *gin.Context) {
	var req struct {
		AppName string `json:"app_name" form:"app_name"`
		Version string `json:"version" form:"version"`
		UUID    string `json:"uuid" form:"uuid"`
		GiftID  string `json:"gift_id" form:"gift_id"`
	}

	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "请求参数错误",
		})
		return
	}

	if req.AppName == "" || req.UUID == "" || req.GiftID == "" {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 模拟消费圈子礼品
	consumeResult := map[string]interface{}{
		"gift_id":        req.GiftID,
		"consume_status": "success",
		"rewards": []map[string]interface{}{
			{"type": "coins", "amount": 1000},
			{"type": "exp", "amount": 50},
		},
		"consume_time": 1234567890,
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: consumeResult,
	})
}
